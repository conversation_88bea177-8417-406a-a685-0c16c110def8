import {
	Controller,
	Get,
	Post,
	Body,
	Param,
	Query,
	UseGuards,
	Res,
	Req,
	ValidationPipe,
	HttpException,
	HttpStatus
} from '@nestjs/common';
import {
	ApiBearerAuth,
	ApiOperation,
	ApiTags,
	ApiResponse
} from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { AnalyticsService } from './analytics.service';
import { SendDocuments } from '../utils/common/send-document.service';
import { Request } from 'express';

interface RequestWithUser extends Request {
	user: {
		id: string;
		role: Role;
		clinicId?: string;
		brandId?: string;
	};
}
import {
	DownloadAnalyticsReportDto,
	GetRevenueChartDataDto,
	RevenueChartDataPoint,
	CollectedPaymentsChartDataPoint,
	GetAppointmentsChartDataDto,
	AppointmentsChartResponse,
	DoctorSummaryResponseDto,
	GetDoctorSummaryDto,
	SummaryResponseDto,
	GetSummaryDto
} from './dto/analytics.dto';
import {
	ShareAnalyticsDocumentsDto,
	ShareAnalyticsDocumentsResponseDto,
	AnalyticsDocumentStatusResponseDto
} from './dto/share-analytics-documents.dto';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@ApiTags('Analytics')
@Controller('analytics')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class AnalyticsController {
	constructor(
		private readonly analyticsService: AnalyticsService,
		private readonly sendDocuments: SendDocuments
	) {}

	@Get('revenue-chart-data')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get revenue chart data' })
	@TrackMethod('get-revenue-chart-data')
	async getRevenueChartData(
		@Query(new ValidationPipe({ transform: true }))
		dto: GetRevenueChartDataDto
	): Promise<RevenueChartDataPoint[]> {
		return await this.analyticsService.getRevenueChartData(dto);
	}

	@Get('collected-payments-chart-data')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get collected payments chart data' })
	@TrackMethod('get-collected-payments-chart-data')
	async getCollectedPaymentsChartData(
		@Query(new ValidationPipe({ transform: true }))
		dto: GetRevenueChartDataDto
	): Promise<CollectedPaymentsChartDataPoint[]> {
		return await this.analyticsService.getCollectedPaymentsChartData(dto);
	}

	@Get('appointments-chart-data')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get appointments chart data' })
	@TrackMethod('get-appointments-chart-data')
	async getAppointmentsChartData(
		@Query(new ValidationPipe({ transform: true }))
		dto: GetAppointmentsChartDataDto
	): Promise<AppointmentsChartResponse> {
		return await this.analyticsService.getAppointmentsChartData(dto);
	}

	@Get('download-report')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Download analytics report' })
	@TrackMethod('download-analytics-report')
	async downloadReport(
		@Query(new ValidationPipe({ transform: true }))
		dto: DownloadAnalyticsReportDto,
		@Res() res: Response
	): Promise<void> {
		const buffer = await this.analyticsService.generateReport(dto);

		res.set({
			'Content-Type':
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'Content-Disposition': `attachment; filename="${dto.type}-report.xlsx"`,
			'Content-Length': buffer.length,
			'Cache-Control': 'no-cache'
		});

		res.end(buffer);
	}

	@Get('doctor-summary')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get doctor performance summary' })
	@TrackMethod('get-doctor-summary')
	async getDoctorSummary(
		@Query(new ValidationPipe({ transform: true }))
		dto: GetDoctorSummaryDto
	): Promise<DoctorSummaryResponseDto[]> {
		return await this.analyticsService.getDoctorSummary(dto);
	}

	@Get('summary')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get clinic performance summary' })
	@ApiResponse({
		status: 200,
		description: 'Returns summary data for clinic'
	})
	@TrackMethod('get-clinic-summary')
	async getSummary(
		@Query(new ValidationPipe({ transform: true })) dto: GetSummaryDto
	): Promise<SummaryResponseDto> {
		return await this.analyticsService.getSummary(dto);
	}

	@Post('share-documents')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Share analytics documents via email' })
	@ApiResponse({
		status: 200,
		description: 'Analytics document sharing request created successfully',
		type: ShareAnalyticsDocumentsResponseDto
	})
	@TrackMethod('share-analytics-documents')
	async shareAnalyticsDocuments(
		@Body(new ValidationPipe({ transform: true })) shareRequest: ShareAnalyticsDocumentsDto,
		@Req() req: RequestWithUser
	): Promise<ShareAnalyticsDocumentsResponseDto> {
		const {
			clinicId,
			brandId,
			ownerId,
			documentTypes,
			startDate,
			endDate,
			recipientEmail,
			recipientType
		} = shareRequest;

		// CRITICAL SECURITY: Validate user can only access their own clinic's data
		if (req.user.clinicId && req.user.clinicId !== clinicId) {
			throw new HttpException(
				'Access denied: You can only request documents for your own clinic',
				HttpStatus.FORBIDDEN
			);
		}

		if (req.user.brandId && req.user.brandId !== brandId) {
			throw new HttpException(
				'Access denied: You can only request documents for your own brand',
				HttpStatus.FORBIDDEN
			);
		}

		// BUSINESS RULE: Validate time period is not more than 1 month
		const startDateObj = new Date(startDate);
		const endDateObj = new Date(endDate);

		// Calculate the difference in days
		const timeDifference = endDateObj.getTime() - startDateObj.getTime();
		const daysDifference = Math.ceil(timeDifference / (1000 * 3600 * 24));

		if (daysDifference > 31) {
			throw new HttpException(
				'Time period cannot exceed 31 days. Please select a shorter date range.',
				HttpStatus.BAD_REQUEST
			);
		}

		if (startDateObj > endDateObj) {
			throw new HttpException(
				'Start date cannot be after end date.',
				HttpStatus.BAD_REQUEST
			);
		}

		// Prevent future dates
		const today = new Date();
		today.setHours(23, 59, 59, 999); // End of today
		if (endDateObj > today) {
			throw new HttpException(
				'End date cannot be in the future.',
				HttpStatus.BAD_REQUEST
			);
		}

		return await this.sendDocuments.shareAnalyticsDocuments(
			clinicId,
			brandId,
			ownerId,
			req.user.id,
			documentTypes,
			new Date(startDate),
			new Date(endDate),
			recipientEmail,
			recipientType
		);
	}

	@Get('share-documents/:requestId/status')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get analytics document sharing status' })
	@ApiResponse({
		status: 200,
		description: 'Returns the status of analytics document sharing request',
		type: AnalyticsDocumentStatusResponseDto
	})
	@TrackMethod('get-analytics-document-status')
	async getDocumentShareStatus(
		@Param('requestId') requestId: string,
		@Req() req: RequestWithUser
	): Promise<AnalyticsDocumentStatusResponseDto> {
		return await this.sendDocuments.getAnalyticsDocumentStatus(requestId, req.user);
	}
}
