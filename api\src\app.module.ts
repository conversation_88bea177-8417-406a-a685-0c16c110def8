import {
	DynamicModule,
	MiddlewareConsumer,
	Module,
	NestModule
} from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import appConfig from './config/app.config';
import awsConfig from './config/aws.config';
import configuration from './config/config';
import databaseConfig from './config/database.config';
import googleConfig from './config/google.config';
import { DatabaseModule } from './database/database.module';
import { HealthModule } from './health/health.module';
import { UsersModule } from './users/users.module';
import { AwsCommonModule } from './utils/aws/aws-module';
import { LoggerModule } from './utils/logger/logger-module';
import { LoggingMiddleware } from './utils/middlewares/logRequest.middleware';
import { MiddlewareModule } from './utils/middlewares/middlewares.module';
import { RateLimitingMiddleware } from './utils/middlewares/rate-limiting.middleware';
import { VersionCheckModule } from './app-versioning/version-check.module';
import { PatientsModule } from './patients/patients.module';
import { OwnersModule } from './owners/owners.module';
import { PatientAlertsModule } from './patient-alerts/patientAlerts.module';
import { AppointmentsModule } from './appointments/appointments.module';
import { ClinicModule } from './clinics/clinic.module';
import { ClinicLabReportModule } from './clinic-lab-report/clinic-lab-report.module';
import { ClinicMedicationsModule } from './clinic-medications/clinic-medications.module';
import { ClinicPlansModule } from './clinic-plans/clinic-plans.module';
import { AppointmentAssessmentModule } from './appointment-assessment/appointment-assessment.module';
import { TasksModule } from './tasks/tasks.module';
import * as cors from 'cors';
import { S3Module } from './utils/aws/s3/s3.module';
import { SESModule } from './utils/aws/ses/ses.module';
import { SocketModule } from './socket/socket.module';
import { ChatRoomModule } from './chat-room/chat-room.module';
import { UserOtpModule } from './user-otps/user-otps.module';
import { BrandsModule } from './brands/brands.module';
import { LongTermMedicationsModule } from './long-term-medications/long-term-medications.module';
import { RoleModule } from './roles/role.module';
import { ClinicAlertsModule } from './clinic-alerts/clinic-alerts.module';
import { ScheduleModule } from '@nestjs/schedule';
import { CartItemModule } from './cart-items/cart-item.module';
import { ClinicConsumablesModule } from './clinic-consumables/clinic-consumables.module';
import { ClinicProductsModule } from './clinic-products/clinic-products.module';
import { ClinicServicesModule } from './clinic-services/clinic-services.module';
import { ClinicVaccinationsModule } from './clinic-vaccinations/clinic-vaccinations.module';
import { PatientVaccinationsModule } from './patient-vaccinations/patient-vaccinations.module';
import { CartsModule } from './carts/carts.module';
import { InvoiceModule } from './invoice/invoice.module';
import { PaymentDetailsModule } from './payment-details/payment-details.module';
import { DataModule } from './data-migration/data.module';
import { WhatsappModule } from './utils/whatsapp-integration/whatsapp.module';
import { ClinicIdexxModule } from './clinic_integrations/idexx/clinic-idexx.module';
import { EmrModule } from './emr/emr.module';
import { AIModule } from './utils/ai-integration/soap-ai/ai.module';
import { PatientRemindersModule } from './patient-reminders/patient-reminder.module';
import { DocumentLibraryModule } from './document-library/document-library.module';
import { PatientDocumentLibrariesModule } from './patient-document-libraries/patient-document-libraries.module';
import { GlobalReminderModule } from './patient-global-reminders/global-reminders.module';
import { SqsModule } from './utils/aws/sqs/sqs.module';
import { NewRelicMiddleware } from './utils/middlewares/newrelic.middleware';
import { DiagnosticTemplatesModule } from './diagnostic-notes-templates/diagnostic-note.module';
import { AnalyticsModule } from './analytics/analytics.module';
import { CronHelperModule } from './utils/cron/cronHelper.module';
import { PatientEstimateModule } from './patient-estimate/patient-estimate.module';
import { TabActivityModule } from './tab-activity/tab-activity.module';
import { CreditsModule } from './credits/credits.module';
import { RedisModule } from './utils/redis/redis.module';
import { SessionModule } from './session/session.module';
import { ClientDashboardModule } from './client-dashboard/client-dashboard.module';
import { StatementModule } from './statement/statement.module';
import { PetTransferModule } from './pet-transfer/pet-transfer.module';
import { ClinicDeletionModule } from './clinic-deletion/clinic-deletion.module';
import { APP_FILTER } from '@nestjs/core';
import { SessionEndedFilter } from './filters/session-ended.filter';

@Module({
	imports: [
		ConfigModule.forRoot({
			isGlobal: true,
			load: [
				appConfig,
				awsConfig,
				googleConfig,
				databaseConfig,
				configuration
			],
			envFilePath: ['.env']
		}),
		DatabaseModule,
		LoggerModule,
		MiddlewareModule,
		UsersModule,
		HealthModule,
		AwsCommonModule,
		AuthModule,
		VersionCheckModule,
		PatientsModule,
		OwnersModule,
		ClinicModule,
		PatientAlertsModule,
		AppointmentsModule,
		ClinicLabReportModule,
		ClinicMedicationsModule,
		ClinicPlansModule,
		AppointmentAssessmentModule,
		SESModule,
		// SocketModule,
		UserOtpModule,
		BrandsModule,
		LongTermMedicationsModule,
		S3Module,
		TasksModule,
		SESModule,
		ChatRoomModule,
		RoleModule,
		ClinicConsumablesModule,
		ClinicProductsModule,
		ClinicServicesModule,
		ClinicVaccinationsModule,
		ClinicAlertsModule,
		ScheduleModule.forRoot(),
		PatientVaccinationsModule,
		CartItemModule,
		CartsModule,
		InvoiceModule,
		PaymentDetailsModule,
		DataModule,
		WhatsappModule,
		ClinicIdexxModule,
		AIModule,
		PatientRemindersModule,
		EmrModule,
		DocumentLibraryModule,
		AIModule,
		PatientDocumentLibrariesModule,
		AnalyticsModule,
		GlobalReminderModule,
		DiagnosticTemplatesModule,
		CronHelperModule.register({ enableCronJobs: false }),
		PatientEstimateModule,
		TabActivityModule,
		CreditsModule,
		RedisModule,
		SessionModule,
		ClientDashboardModule,
		StatementModule,
		PetTransferModule,
		ClinicDeletionModule
	],
	controllers: [AppController],
	providers: [
		AppService,
		{ provide: APP_FILTER, useClass: SessionEndedFilter }
	]
})
export class AppModule implements NestModule {
	configure(consumer: MiddlewareConsumer) {
		consumer
			.apply(
				cors(),
				LoggingMiddleware,
				RateLimitingMiddleware,
				NewRelicMiddleware
			)
			.forRoutes('*');
	}
	static register(config: { isSqsEnabled: boolean }): DynamicModule {
		return {
			module: AppModule,
			imports: [SqsModule.forRoot(config.isSqsEnabled)]
		};
	}
}
