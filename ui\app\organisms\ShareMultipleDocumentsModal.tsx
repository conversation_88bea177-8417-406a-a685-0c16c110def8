import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Modal } from '../molecules';
import { Button, Checkbox, Text } from '../atoms';
import RadioButtonGroup, {
    RadioButtonItemsT,
} from '@/app/molecules/RadiobuttonGroup';
import RenderFields from '../molecules/RenderFields';
import classNames from 'classnames';
import { parsePhoneNumber } from 'react-phone-number-input';

interface ShareMultipleDocumentsModalProps {
    isOpen: boolean;
    onClose: () => void;
    handleCancel: () => void;
    handleShare: (data: FormValues) => void;
    documentAvailability: Record<string, boolean>;
    title: string;
}

// Export the FormValues type
export type FormValues = {
    recipient: 'client' | 'other';
    shareViaEmail: boolean;
    shareViaWhatsapp: boolean;
    email?: string;
    number?: string;
    phoneNumberNational?: string;
    countryCode?: string;
};

const Schema = yup.object().shape({
    recipient: yup
        .string()
        .oneOf(['client', 'other'] as const)
        .required('Recipient is required'),
    shareViaEmail: yup.boolean().required(),
    shareViaWhatsapp: yup.boolean().required(),
    email: yup
        .string()
        .optional()
        .when(['recipient', 'shareViaEmail'], {
            is: (recipient: FormValues['recipient'], shareViaEmail: boolean) =>
                recipient === 'other' && shareViaEmail,
            then: (schema) =>
                schema
                    .required('Email is required when sharing via email')
                    .email('Must be a valid email'),
        }),
    number: yup
        .string()
        .optional()
        .when(['recipient', 'shareViaWhatsapp'], {
            is: (
                recipient: FormValues['recipient'],
                shareViaWhatsapp: boolean
            ) => recipient === 'other' && shareViaWhatsapp,
            then: (schema) =>
                schema
                    .required('Number is required when sharing via WhatsApp')
                    .test(
                        'is-valid-phone',
                        'Must be a valid phone number',
                        (value) => {
                            try {
                                const phone = parsePhoneNumber(value || '');
                                return !!phone && phone.isValid();
                            } catch (error) {
                                return false;
                            }
                        }
                    ),
        }),
    phoneNumberNational: yup.string().optional(),
    countryCode: yup.string().optional(),
});

const ShareMultipleDocumentsModal: React.FC<
    ShareMultipleDocumentsModalProps
> = ({
    isOpen,
    onClose,
    handleCancel,
    handleShare,
    documentAvailability,
    title,
}) => {
    const radioButtonItems: RadioButtonItemsT[] = [
        {
            id: 'client',
            label: 'Client',
            value: 'client',
            defaultChecked: true,
        },
        { id: 'other', label: 'Other', value: 'other' },
    ];

    const {
        control,
        register,
        formState: { errors, isValid },
        handleSubmit,
        setValue,
        watch,
        trigger,
        reset,
    } = useForm<FormValues>({
        resolver: yupResolver(Schema),
        defaultValues: {
            recipient: 'client',
            shareViaEmail: true,
            shareViaWhatsapp: false,
            email: '',
            number: '',
            phoneNumberNational: '',
            countryCode: '',
        },
        mode: 'all',
    });

    // State for phone number input
    const [phoneNumber, setPhoneNumberState] = useState<string>(
        watch('number') || '' // Ensure initial value is string
    );
    const [phoneNumberNational, setPhoneNumberNational] = useState<string>('');
    const [countryCode, setCountryCode] = useState<string>('');

    // Reset form when modal opens
    React.useEffect(() => {
        if (isOpen) {
            reset({
                recipient: 'client',
                shareViaEmail: true,
                shareViaWhatsapp: false,
                email: '',
                number: '',
                phoneNumberNational: '',
                countryCode: '',
            });
            // Reset local state as well
            setPhoneNumberState('');
            setPhoneNumberNational('');
            setCountryCode('');
        }
    }, [isOpen, reset]);

    // Handler for phone number input changes
    const handlePhoneNumberChange = (value: string | undefined) => {
        const val = value || '';
        setPhoneNumberState(val);
        const phone = parsePhoneNumber(val);
        setPhoneNumberNational(phone?.nationalNumber || '');
        setCountryCode(phone?.countryCallingCode || '');
    };

    // Effect to sync local phone state back to React Hook Form
    useEffect(() => {
        const currentRawInput = phoneNumber || '';
        const phone = parsePhoneNumber(currentRawInput);

        // Only update the RHF 'number' field with the valid E.164 number
        if (phone && phone.isValid()) {
            setValue('number', phone.number); // Use E.164 format
        } else {
            setValue('number', currentRawInput);
        }
        setValue('phoneNumberNational', phoneNumberNational);
        setValue('countryCode', countryCode);
        // Trigger validation when phone number changes programmatically
        if (watch('recipient') === 'other' && watch('shareViaWhatsapp')) {
            trigger('number');
        }
    }, [
        phoneNumber,
        phoneNumberNational,
        countryCode,
        setValue,
        trigger,
        watch,
    ]);

    const onSubmit = (data: FormValues) => {
        // Pass the entire data object
        handleShare(data);
    };

    const recipient = watch('recipient');
    const shareViaEmail = watch('shareViaEmail');
    const shareViaWhatsapp = watch('shareViaWhatsapp');

    const isFormValid = () => {
        if (recipient === 'client') {
            return shareViaEmail || shareViaWhatsapp;
        }

        if (shareViaEmail && !shareViaWhatsapp) {
            return !!watch('email') && !errors.email;
        }

        if (shareViaWhatsapp && !shareViaEmail) {
            // Use the local state for validation check as RHF state might lag
            return !!phoneNumber && !errors.number;
        }

        if (shareViaEmail && shareViaWhatsapp) {
            return (
                !!watch('email') &&
                !!phoneNumber && // Use local state here too
                !errors.email &&
                !errors.number
            );
        }

        return false;
    };
    return (
        <Modal
            isOpen={isOpen}
            isHeaderBorder={true}
            isModalSubTitle={true}
            modalSubTitle=""
            modalTitle={title}
            onClose={onClose}
            dataAutomation="share-emr"
            modalFooter={
                <div className="w-full flex gap-5 justify-end">
                    <Button
                        id="cancel"
                        variant="secondary"
                        type="button"
                        onClick={handleCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        id="share"
                        variant="primary"
                        type="submit"
                        form="share-form"
                        disabled={!isFormValid()}
                    >
                        Share
                    </Button>
                </div>
            }
        >
            <form
                id="share-form"
                onSubmit={handleSubmit(onSubmit)}
                className="gap-5 flex flex-col mb-2"
            >
                <div>
                    <Text
                        variant="bodySmall"
                        fontWeight="font-semibold"
                        className="mb-2"
                    >
                        Please select the recipient
                    </Text>
                    <Controller
                        name="recipient"
                        control={control}
                        render={({ field }) => (
                            <RadioButtonGroup
                                id="selectTheRecipient"
                                name="recipient"
                                direction="column"
                                radioButtonItems={radioButtonItems}
                                isRequired={true}
                                errorMessage={errors.recipient?.message}
                                size="medium"
                                required={true}
                                onChange={(selectedOption) => {
                                    field.onChange(selectedOption?.value);
                                    if (selectedOption?.value === 'client') {
                                        setValue('email', '');
                                        setValue('number', '');
                                        // Also reset phone state/fields if switching to client
                                        setPhoneNumberState('');
                                        setPhoneNumberNational('');
                                        setCountryCode('');
                                        setValue('phoneNumberNational', '');
                                        setValue('countryCode', '');
                                    }
                                    trigger(['email', 'number']); // Trigger validation for both fields
                                }}
                                value={field.value}
                            />
                        )}
                    />
                </div>

                <div className="flex flex-col gap-1">
                    <Text variant="bodySmall" fontWeight="font-semibold">
                        Please select the mode of sharing the document
                    </Text>
                    <div className="flex flex-col gap-2">
                        <div
                            className={classNames(
                                shareViaEmail
                                    ? 'bg-[#EAECEB] rounded-lg'
                                    : 'border-2 rounded-lg'
                            )}
                        >
                            <Checkbox
                                id="shareViaEmail"
                                label="Share via Email"
                                name="shareViaEmail"
                                register={register}
                                className={classNames('p-4')}
                            />
                        </div>
                        {recipient === 'other' && shareViaEmail && (
                            <div className="flex flex-col gap-2">
                                <RenderFields
                                    control={control}
                                    fields={[
                                        {
                                            id: 'email-input',
                                            type: 'text-input',
                                            label: 'Email Address',
                                            name: 'email',
                                            fieldVariant: 'basicField',
                                            placeholder: 'Enter email address',
                                            required: true,
                                            onChange: (e: any) => {
                                                setValue(
                                                    'email',
                                                    e?.target?.value
                                                );
                                                trigger('email');
                                            },
                                        },
                                    ]}
                                    setValue={setValue}
                                    watch={watch}
                                    errors={errors}
                                />
                            </div>
                        )}
                        <div
                            className={classNames(
                                shareViaWhatsapp
                                    ? 'bg-[#EAECEB] rounded-lg'
                                    : 'border-2 rounded-lg'
                            )}
                        >
                            <Checkbox
                                id="shareViaWhatsapp"
                                label="Share via WhatsApp"
                                name="shareViaWhatsapp"
                                register={register}
                                className={classNames('p-4')}
                            />
                        </div>
                        {recipient === 'other' && shareViaWhatsapp && (
                            <div className="flex flex-col gap-2 max-w-[260px]">
                                <RenderFields
                                    control={control}
                                    errors={errors}
                                    register={register}
                                    fields={[
                                        {
                                            id: 'phone-input',
                                            type: 'number-with-country',
                                            label: 'Phone Number',
                                            name: 'number',
                                            placeholder: 'Enter phone number',
                                            required: true,
                                            value: phoneNumber,
                                            onChange: handlePhoneNumberChange,
                                            fieldSize: 'medium',
                                        },
                                    ]}
                                    setValue={setValue}
                                    watch={watch}
                                />
                            </div>
                        )}
                    </div>
                </div>
            </form>
        </Modal>
    );
};

export default ShareMultipleDocumentsModal;
